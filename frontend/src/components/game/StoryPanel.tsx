// Story Panel Component - Main storytelling interface

'use client';

import React, { useEffect, useRef, useState } from 'react';
import { useCurrentSession, useCurrentStory, useIsAIGenerating } from '@/stores/gameStore';
import { useGameAction } from '@/hooks/useGameAction';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { Card } from '@/components/ui/Card';
import { TypingIndicator } from '@/components/ui/Loading';
import { cn, truncateText } from '@/utils/helpers';
import { gameFormatters, dateFormatters } from '@/utils/formatting';
import { actionSchema } from '@/utils/validation';
import { StoryEntry, ActionType } from '@/types';
import { ACTION_TYPES } from '@/utils/constants';
import {
  PaperAirplaneIcon,
  ArrowPathIcon,
} from '@heroicons/react/24/outline';

export function StoryPanel() {
  const currentSession = useCurrentSession();
  const story = useCurrentStory();
  const isAIGenerating = useIsAIGenerating();
  
  const [actionInput, setActionInput] = useState('');
  const [actionError, setActionError] = useState('');
  const storyEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const { performAction, isLoading, error } = useGameAction(
    currentSession?.session_id || '',
    {
      enableOptimisticUpdates: true,
      enableWebSocket: true,
      onSuccess: () => {
        setActionInput('');
        setActionError('');
      },
      onError: (err) => {
        setActionError(err.message);
      },
    }
  );

  // Auto-scroll to bottom when new story entries are added
  useEffect(() => {
    if (storyEndRef.current) {
      storyEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [story]);

  // Focus input when component mounts
  useEffect(() => {
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  const handleSubmitAction = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!actionInput.trim()) {
      setActionError('Please enter an action');
      return;
    }

    // Validate action
    const validation = actionSchema.safeParse(actionInput.trim());
    if (!validation.success) {
      setActionError(validation.error.errors[0].message);
      return;
    }

    try {
      await performAction(actionInput.trim());
    } catch (err) {
      // Error is handled by the hook
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmitAction(e);
    }
  };

  const getEntryIcon = (type: ActionType) => {
    switch (type) {
      case ACTION_TYPES.PLAYER:
        return '👤';
      case ACTION_TYPES.NARRATION:
        return '📖';
      case ACTION_TYPES.ACTION:
        return '⚡';
      case ACTION_TYPES.SYSTEM:
        return '🔧';
      default:
        return '💬';
    }
  };

  const getEntryStyle = (type: ActionType) => {
    switch (type) {
      case ACTION_TYPES.PLAYER:
        return 'bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800';
      case ACTION_TYPES.NARRATION:
        return 'bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-700';
      case ACTION_TYPES.ACTION:
        return 'bg-green-50 dark:bg-green-950 border-green-200 dark:border-green-800';
      case ACTION_TYPES.SYSTEM:
        return 'bg-yellow-50 dark:bg-yellow-950 border-yellow-200 dark:border-yellow-800';
      default:
        return 'bg-gray-50 dark:bg-gray-900 border-gray-200 dark:border-gray-700';
    }
  };

  if (!currentSession) {
    return (
      <div className="h-full flex items-center justify-center">
        <p className="text-muted-foreground">No active game session</p>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col">
      {/* Story Display */}
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {story.length === 0 ? (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-foreground mb-2">
              Your Adventure Begins
            </h3>
            <p className="text-muted-foreground mb-4">
              Enter your first action to start your AI-driven story adventure.
            </p>
          </div>
        ) : (
          story.map((entry: StoryEntry, index: number) => (
            <Card
              key={entry.id}
              className={cn(
                'p-4 transition-all duration-200',
                getEntryStyle(entry.type as ActionType)
              )}
            >
              <div className="flex items-start space-x-3">
                <span className="text-lg flex-shrink-0 mt-1">
                  {getEntryIcon(entry.type as ActionType)}
                </span>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-foreground">
                      {gameFormatters.actionType(entry.type as ActionType)}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {gameFormatters.storyTimestamp(entry.timestamp)}
                    </span>
                  </div>
                  <div className="prose prose-sm dark:prose-invert max-w-none">
                    <p className="text-foreground whitespace-pre-wrap">
                      {entry.text}
                    </p>
                  </div>
                </div>
              </div>
            </Card>
          ))
        )}

        {/* AI Generating Indicator */}
        {isAIGenerating && (
          <Card className="p-4 bg-blue-50 dark:bg-blue-950 border-blue-200 dark:border-blue-800">
            <div className="flex items-center space-x-3">
              <span className="text-lg">🤖</span>
              <TypingIndicator message="AI is crafting your story..." />
            </div>
          </Card>
        )}

        <div ref={storyEndRef} />
      </div>

      {/* Action Input */}
      <div className="border-t border-border bg-card p-6">
        <form onSubmit={handleSubmitAction} className="space-y-4">
          <div className="flex space-x-2">
            <Input
              ref={inputRef}
              value={actionInput}
              onChange={(e) => setActionInput(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder="What do you do? (Press Enter to submit)"
              error={actionError || error?.message}
              className="flex-1"
              disabled={isLoading || isAIGenerating}
              maxLength={500}
            />
            <Button
              type="submit"
              disabled={isLoading || isAIGenerating || !actionInput.trim()}
              loading={isLoading}
              className="px-4"
            >
              <PaperAirplaneIcon className="h-4 w-4" />
            </Button>
          </div>

          {/* Action Suggestions */}
          <div className="flex flex-wrap gap-2">
            {currentSession.world_state.available_actions?.slice(0, 3).map((suggestion, index) => (
              <Button
                key={index}
                variant="outline"
                size="sm"
                onClick={() => setActionInput(suggestion)}
                disabled={isLoading || isAIGenerating}
                className="text-xs"
              >
                {truncateText(suggestion, 30)}
              </Button>
            ))}
          </div>

          {/* Character count */}
          <div className="flex justify-between items-center text-xs text-muted-foreground">
            <span>
              {actionInput.length}/500 characters
            </span>
            <span>
              Press Enter to submit, Shift+Enter for new line
            </span>
          </div>
        </form>
      </div>
    </div>
  );
}

export default StoryPanel;
