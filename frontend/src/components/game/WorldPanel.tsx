// World Panel Component - World state visualization

'use client';

import React from 'react';
import { useCurrentWorldState } from '@/stores/gameStore';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { cn } from '@/utils/helpers';
import { textFormatters } from '@/utils/formatting';
import { WorldState } from '@/types';
import {
  GlobeAltIcon,
  MapPinIcon,
  ClockIcon,
  CloudIcon,
  ExclamationTriangleIcon,
  UserGroupIcon,
  SparklesIcon,
} from '@heroicons/react/24/outline';

export function WorldPanel() {
  const worldState = useCurrentWorldState();

  if (!worldState) {
    return (
      <div className="h-full flex items-center justify-center p-6">
        <div className="text-center">
          <GlobeAltIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-medium text-foreground mb-2">
            No World Data
          </h3>
          <p className="text-muted-foreground">
            Start a game session to explore the world.
          </p>
        </div>
      </div>
    );
  }

  const getWeatherIcon = (weather: string) => {
    const weatherIcons: Record<string, string> = {
      sunny: '☀️',
      cloudy: '☁️',
      rainy: '🌧️',
      stormy: '⛈️',
      snowy: '❄️',
      foggy: '🌫️',
      windy: '💨',
    };
    return weatherIcons[weather.toLowerCase()] || '🌤️';
  };

  const getTimeIcon = (timeOfDay: string) => {
    const timeIcons: Record<string, string> = {
      dawn: '🌅',
      morning: '🌄',
      noon: '☀️',
      afternoon: '🌇',
      evening: '🌆',
      dusk: '🌇',
      night: '🌙',
      midnight: '🌚',
    };
    return timeIcons[timeOfDay.toLowerCase()] || '🕐';
  };

  const renderInfoCard = (
    title: string,
    value: string,
    icon: React.ReactNode,
    description?: string
  ) => (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center">
          {icon}
          <span className="ml-2">{title}</span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <p className="text-2xl font-bold text-foreground mb-1">
          {textFormatters.titleCase(value)}
        </p>
        {description && (
          <p className="text-sm text-muted-foreground">
            {description}
          </p>
        )}
      </CardContent>
    </Card>
  );

  const renderListCard = (
    title: string,
    items: string[],
    icon: React.ReactNode,
    emptyMessage: string
  ) => (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg flex items-center">
          {icon}
          <span className="ml-2">{title}</span>
          <span className="ml-auto text-sm font-normal text-muted-foreground">
            {items.length}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {items.length === 0 ? (
          <p className="text-muted-foreground text-center py-4">
            {emptyMessage}
          </p>
        ) : (
          <div className="space-y-2">
            {items.map((item, index) => (
              <div
                key={index}
                className="p-2 bg-muted rounded-lg text-sm"
              >
                {textFormatters.titleCase(item)}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="h-full overflow-y-auto p-6 space-y-6">
      {/* Current Location */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
        <CardHeader>
          <CardTitle className="text-2xl flex items-center">
            <MapPinIcon className="h-6 w-6 mr-3 text-blue-600" />
            Current Location
          </CardTitle>
        </CardHeader>
        <CardContent>
          <h2 className="text-3xl font-bold text-foreground mb-2">
            {textFormatters.titleCase(worldState.current_location)}
          </h2>
          <p className="text-muted-foreground">
            You are currently exploring this area. Look around to discover new opportunities and adventures.
          </p>
        </CardContent>
      </Card>

      {/* Time and Weather */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {renderInfoCard(
          'Time of Day',
          worldState.time_of_day,
          <ClockIcon className="h-5 w-5 text-orange-500" />,
          `It is currently ${worldState.time_of_day.toLowerCase()} in the world.`
        )}

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-lg flex items-center">
              <CloudIcon className="h-5 w-5 text-blue-500" />
              <span className="ml-2">Weather</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-3">
              <span className="text-3xl">
                {getWeatherIcon(worldState.weather)}
              </span>
              <div>
                <p className="text-2xl font-bold text-foreground">
                  {textFormatters.titleCase(worldState.weather)}
                </p>
                <p className="text-sm text-muted-foreground">
                  Current weather conditions
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Special Conditions */}
      {worldState.special_conditions && worldState.special_conditions.length > 0 && (
        <Card className="border-yellow-200 dark:border-yellow-800">
          <CardHeader>
            <CardTitle className="text-lg flex items-center text-yellow-700 dark:text-yellow-300">
              <ExclamationTriangleIcon className="h-5 w-5 mr-2" />
              Special Conditions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {worldState.special_conditions.map((condition, index) => (
                <div
                  key={index}
                  className="p-3 bg-yellow-50 dark:bg-yellow-950 border border-yellow-200 dark:border-yellow-800 rounded-lg"
                >
                  <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                    {textFormatters.titleCase(condition)}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* NPCs Present */}
      {renderListCard(
        'NPCs Present',
        worldState.npcs_present || [],
        <UserGroupIcon className="h-5 w-5 text-green-500" />,
        'No NPCs are currently in this area.'
      )}

      {/* Available Actions */}
      {renderListCard(
        'Available Actions',
        worldState.available_actions || [],
        <SparklesIcon className="h-5 w-5 text-purple-500" />,
        'No special actions are available at this location.'
      )}

      {/* World Metadata */}
      {worldState.metadata && Object.keys(worldState.metadata).length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">Additional Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(worldState.metadata).map(([key, value]) => (
                <div key={key} className="p-3 bg-muted rounded-lg">
                  <p className="text-sm font-medium text-muted-foreground mb-1">
                    {textFormatters.camelToReadable(key)}
                  </p>
                  <p className="text-sm text-foreground">
                    {typeof value === 'object' ? JSON.stringify(value) : String(value)}
                  </p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* World Exploration Tips */}
      <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950 dark:to-emerald-950">
        <CardHeader>
          <CardTitle className="text-lg flex items-center text-green-700 dark:text-green-300">
            <GlobeAltIcon className="h-5 w-5 mr-2" />
            Exploration Tips
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-green-700 dark:text-green-300">
            <p>• Use the Story panel to interact with your environment</p>
            <p>• Pay attention to NPCs and special conditions</p>
            <p>• Available actions show what you can do in this location</p>
            <p>• Weather and time may affect your adventures</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

export default WorldPanel;
